# Story 1.3: Lead List Management & Display

## Status

Approved

## Story

**As a** sales team member,
**I want** to view and manage my imported leads in an organized, searchable list on a dedicated Leads page,
**so that** I can easily navigate through prospects and select specific leads for content generation.

## Acceptance Criteria

1. Lead data displayed using ShadCN DataTable component with sorting capabilities
2. Search functionality filters leads by company name, contact name, or email
3. Batch selection checkboxes enable multi-lead operations
4. Lead count and status indicators show total imported vs. processed leads
5. Individual lead detail view shows all available prospect information
6. Filter options for company, title, or processing status
7. Pagination or virtualization handles large lead lists (1000+) efficiently
8. Generate button/action available for selected leads to trigger content generation
9. Local storage persistence maintains lead data between sessions (temporary - backend integration planned)

## Tasks / Subtasks

- [ ] Create lead data table with ShadCN DataTable (AC: 1)
  - [ ] Install and configure ShadCN DataTable component (Card, Button, Badge already available from 1.1)
  - [ ] Create LeadData interface extending CsvData with processing status
  - [ ] Implement sortable columns for standard fields (company, contact, email, title) and custom columns
  - [ ] Add table headers with clear field labels based on column mapping

- [ ] Implement search and filter functionality (AC: 2, 6)
  - [ ] Build search input component with real-time filtering
  - [ ] Create search logic for company name, contact name, email
  - [ ] Add filter dropdowns for company, title, processing status
  - [ ] Implement combined search and filter logic

- [ ] Add batch selection capabilities (AC: 3, 8)
  - [ ] Implement row selection checkboxes with "select all" option
  - [ ] Create selection state management using React state
  - [ ] Build batch operation toolbar with Generate button for selected leads
  - [ ] Add visual indicators for selected leads
  - [ ] Connect Generate button to trigger content generation workflow

- [ ] Create lead count and status tracking (AC: 4)
  - [ ] Build status indicator component showing total/processed counts
  - [ ] Implement lead processing status tracking
  - [ ] Add visual indicators for lead processing stages
  - [ ] Create progress summary for batch operations

- [ ] Build individual lead detail view (AC: 5)
  - [ ] Create lead detail modal or expandable row
  - [ ] Display all available lead information clearly
  - [ ] Add edit capabilities for lead data if needed
  - [ ] Implement close/navigate functionality

- [ ] Optimize performance for large datasets (AC: 7)
  - [ ] Implement virtualization or pagination for 1000+ leads
  - [ ] Optimize search and filter performance with debouncing
  - [ ] Add loading states for large dataset operations
  - [ ] Test performance with realistic data volumes

- [ ] Integrate with Import page data flow (Story 1.2 dependency)
  - [ ] Update Import.tsx to navigate to dedicated Leads page after successful import
  - [ ] Implement data passing from CsvUpload onConfirm to Leads page state
  - [ ] Create Leads page/route in React Router (separate from Import page)
  - [ ] Add navigation back to Import page functionality

- [ ] Implement local storage persistence (AC: 9)
  - [ ] Create localStorage service for lead data persistence
  - [ ] Implement save/load functionality for imported leads
  - [ ] Add data restoration on page refresh/revisit
  - [ ] Include processing status in persisted data
  - [ ] Add note/warning about temporary storage (backend integration planned)

## Dev Notes

**Story 1.1 & 1.2 Implementation Context:**
Stories 1.1 and 1.2 are complete and provide the foundation:

- **Available ShadCN UI Components**: Button, Card, Badge, Dropdown Menu (from 1.1)
- **Layout Components**: Header, Navigation, Layout with theme provider (from 1.1)
- **CSV Data Structure**: CsvData interface with `data: Record<string, string>[]`, headers, rowCount, errors (from 1.2)
- **Column Mapping**: ColumnMapping interface maps original headers to standard fields (company, contact, email, title, etc.) (from 1.2)
- **Import Flow**: CsvUpload → CsvPreview → onConfirm callback with full data and mapping (from 1.2)

**Data Management Requirements:**
The lead list will receive data from the CSV import component (Story 1.2) via the `onConfirm` callback and needs to maintain state for:

- Original imported lead data (CsvData.data: Record<string, string>[])
- Column mapping from CSV headers to standard fields (ColumnMapping)
- Processing status (imported, generating, reviewed, exported)
- Selection state for batch operations
- Search and filter state

**State Management Architecture:**
Based on the PRD's frontend stack requirements, consider using:

- React Context or Zustand for global lead state management
- Local component state for UI-specific state (search terms, filters)
- Memoization for expensive search and filter operations

**Performance Considerations:**
The PRD requires support for 1000+ leads without performance degradation:

- Virtual scrolling or pagination for large datasets
- Debounced search to prevent excessive re-renders
- Memoized filter functions for complex filtering logic
- Efficient batch selection algorithms

**UI/UX Requirements:**
Following the PRD's "single-page application flow with clear progress indicators":

- Clear visual indicators for lead processing status
- Intuitive batch selection with visual feedback
- Search and filter controls should be discoverable and efficient
- Progress indicators for batch operations

**Integration Points:**

- **Receives data from Story 1.2**: Import page's `onConfirm` callback provides CsvData and ColumnMapping, then navigates to dedicated Leads page
- **Data handoff**: Import.tsx navigates to leads `/` route after successful import, passing lead data
- **State management**: Leads page stores and manages CsvData.data (Record<string, string>[]) with processing status via localStorage (temporary)
- **Column mapping integration**: Use ColumnMapping to display standard field names while preserving original data
- **Triggers Story 1.4**: Generate button on selected leads initiates AI Content Generation workflow
- **LocalStorage Integration**: Persist lead data locally until backend integration (note: temporary solution)

### Testing

**Test File Location:** `src/components/lead-list/__tests__/`

**Test Standards:**

- Component testing for table rendering and interactions
- State management tests for search, filter, and selection
- Performance tests with large datasets
- Accessibility tests for keyboard navigation and screen readers

**Testing Requirements for This Story:**

- DataTable renders lead data correctly with sorting
- Search functionality filters leads accurately
- Batch selection works correctly with multiple leads
- Status tracking updates properly as leads are processed
- Lead detail view displays complete information
- Performance maintained with 1000+ lead datasets
- Accessibility compliance for WCAG AA requirements

## Change Log

| Date       | Version | Description                                                              | Author     |
| ---------- | ------- | ------------------------------------------------------------------------ | ---------- |
| 2025-09-12 | 1.0     | Initial story creation from Epic 1                                       | Sarah (PO) |
| 2025-09-12 | 1.1     | Updated with Story 1.1 & 1.2 completion context, added integration tasks | Bob (SM)   |

## Dev Agent Record

### Agent Model Used

_To be populated during implementation_

### Debug Log References

_To be populated during implementation_

### Completion Notes List

_To be populated during implementation_

### File List

_To be populated during implementation_

## QA Results

_To be populated by QA Agent_
