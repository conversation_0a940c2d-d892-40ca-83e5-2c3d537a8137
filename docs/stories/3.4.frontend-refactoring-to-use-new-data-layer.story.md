# Story 3.4: Frontend Refactoring to Use New Data Layer

## Status
Ready for Development

## Story
**As a** sales team member,
**I want** the React frontend to use the new IPC bridge for all database operations instead of localStorage,
**so that** all application data is properly persisted in the SQLite database and I can reliably access my leads, imports, and generated content across application sessions.

## Acceptance Criteria
1. Rewrite the `src/utils/leadsStorage.ts` file to make asynchronous calls to the new IPC bridge (`window.api`) instead of `localStorage`
2. Update any React components that are affected by this change from synchronous `localStorage` to asynchronous database calls

## Tasks / Subtasks
- [ ] Refactor leadsStorage.ts utility to use IPC bridge (AC: 1)
  - [ ] Replace localStorage calls with window.api.leads.* operations
  - [ ] Convert synchronous functions to async/await pattern
  - [ ] Update function signatures to return promises
  - [ ] Implement proper error handling for IPC operations
  - [ ] Add type safety using IpcResponse<T> types from src/types/api.ts
- [ ] Update React components for async data operations (AC: 2)
  - [ ] Identify all components using leadsStorage functions
  - [ ] Convert useEffect hooks to handle async data loading
  - [ ] Add loading states for database operations
  - [ ] Implement error handling UI for failed database operations
  - [ ] Update state management to handle async data flow
- [ ] Implement imports data layer integration (AC: 1, 2)
  - [ ] Create or update imports utility functions to use window.api.imports.*
  - [ ] Update CSV import workflow to use database storage
  - [ ] Ensure import metadata is properly tracked in database
  - [ ] Update import history and status tracking
- [ ] Implement generated content data layer integration (AC: 1, 2)
  - [ ] Create or update content utility functions to use window.api.content.*
  - [ ] Update content generation workflow to store in database
  - [ ] Ensure content is properly linked to leads via foreign keys
  - [ ] Update content review and editing interfaces
- [ ] Implement mappings data layer integration (AC: 1, 2)
  - [ ] Create or update mappings utility functions to use window.api.mappings.*
  - [ ] Update CSV column mapping workflow to use database storage
  - [ ] Ensure mapping configurations persist across sessions
- [ ] Update application metadata handling (AC: 1, 2)
  - [ ] Replace any localStorage usage for app settings with window.api.metadata.*
  - [ ] Ensure user preferences and configurations persist in database
  - [ ] Update session-based prompt template management if needed
- [ ] Add comprehensive error handling and user feedback (AC: 1, 2)
  - [ ] Implement user-friendly error messages for database failures
  - [ ] Add retry mechanisms for failed database operations
  - [ ] Create error recovery workflows for data synchronization issues
  - [ ] Add loading indicators for all async database operations
- [ ] Write comprehensive unit and integration tests
  - [ ] Test all updated utility functions with mocked window.api
  - [ ] Test React components with async data loading scenarios
  - [ ] Test error handling and edge cases for database operations
  - [ ] Test data migration scenarios from localStorage to database
  - [ ] Test complete user workflows end-to-end with database backend

## Dev Notes

### Previous Story Insights
From Story 3.3 completion:
- Complete IPC bridge layer connecting React frontend to Electron main process
- Secure `window.api` interface exposed to React components for all database operations
- All DAL functions now accessible from React via `window.api.{table}.{operation}()` pattern
- Comprehensive error handling with proper serialization across process boundaries
- Type-safe API definitions in `src/types/api.ts` for frontend consumption
- All API calls return `IpcResponse<T>` with `success: boolean` and `data` or `error` properties
- Use `isApiSuccess()` and `isApiError()` helper functions from `src/types/api.ts`
- Error handling includes specific error types: ValidationError, NotFoundError, ForeignKeyError, etc.
- Bulk operations available for leads import workflows
- Advanced queries available for reporting and analytics features

### Data Models
Based on Stories 3.1, 3.2, and 3.3 implementation, the following window.api functions are available:

**Leads Operations:**
- `window.api.leads.create(data)` - Create single lead record
- `window.api.leads.bulkCreate(leads)` - Bulk create multiple leads efficiently
- `window.api.leads.getAll(options?)` - Get all leads with filtering/pagination/search
- `window.api.leads.getById(id)` - Get specific lead by ID
- `window.api.leads.getByImport(importId)` - Get leads for specific import
- `window.api.leads.update(id, data)` - Update lead record
- `window.api.leads.delete(id)` - Delete lead record
- `window.api.leads.search(query, options?)` - Full-text search across leads

**Imports Operations:**
- `window.api.imports.create(data)` - Create new import record
- `window.api.imports.getAll(options?)` - Get all imports with filtering/pagination
- `window.api.imports.getById(id)` - Get specific import by ID
- `window.api.imports.update(id, data)` - Update import record
- `window.api.imports.delete(id)` - Delete import with cascade handling

**Generated Content Operations:**
- `window.api.content.create(data)` - Create content record
- `window.api.content.getByLead(leadId)` - Get all content for specific lead
- `window.api.content.getByTouchpoint(touchpoint)` - Get content by touchpoint number
- `window.api.content.update(id, data)` - Update content record
- `window.api.content.delete(id)` - Delete content record

**Mappings Operations:**
- `window.api.mappings.create(data)` - Create field mapping
- `window.api.mappings.getByImport(importId)` - Get mappings for import
- `window.api.mappings.getActive()` - Get all active mappings
- `window.api.mappings.update(id, data)` - Update mapping configuration
- `window.api.mappings.delete(id)` - Delete mapping

**App Metadata Operations:**
- `window.api.metadata.get(key)` - Get configuration value
- `window.api.metadata.set(key, value)` - Set configuration value
- `window.api.metadata.delete(key)` - Delete configuration entry
- `window.api.metadata.getAll()` - Get all configuration entries

**Advanced Query Operations:**
- `window.api.queries.getLeadsWithContent(options?)` - Complex joins for reporting
- `window.api.queries.getImportSummary(importId?)` - Import statistics and summaries
- `window.api.queries.getContentStats()` - Content generation analytics
- `window.api.queries.exportData()` - Export functions for various data formats

### API Specifications
All window.api operations return `IpcResponse<T>` with the following structure:
```typescript
interface IpcResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    type: string;
    message: string;
    details?: any;
  };
}
```

Use helper functions from `src/types/api.ts`:
- `isApiSuccess(response)` - Type guard for successful responses
- `isApiError(response)` - Type guard for error responses

Error types include: ValidationError, NotFoundError, ForeignKeyError, DatabaseError, etc.

### Component Specifications
Based on Epic 3 requirements and existing React application structure:
- Update existing React components to handle async data operations
- Add loading states using React state management (useState, useEffect)
- Implement error boundaries for graceful error handling
- Use existing ShadCN UI components for loading indicators and error messages
- Maintain existing component structure and user interface design
- Ensure backward compatibility with existing user workflows

### File Locations
Based on Epic 3 requirements and existing project structure:
- Primary refactoring target: `src/utils/leadsStorage.ts`
- React components: Identify via search in `src/components/` and `src/pages/`
- Type definitions: Use existing `src/types/api.ts` from Story 3.3
- Utility functions: Create additional utilities in `src/utils/` as needed
- Error handling: Implement in existing component error boundaries
- State management: Update existing React Context or Zustand stores

### Testing Requirements
Based on PRD testing requirements and previous stories' testing approach:
- Test files should be created in `src/utils/__tests__/` and `src/components/__tests__/` directories
- Follow existing testing patterns from previous stories using Vitest and React Testing Library
- Test coverage should include async data operations, error handling, and user workflows
- Mock window.api functions for unit testing
- Integration tests should verify end-to-end data flow from React to database
- Test data migration scenarios from localStorage to database operations

### Technical Constraints
- Must use `window.api.*` interface - direct database access not available in renderer
- All operations are async and return promises
- Error handling must check `response.success` before accessing `response.data`
- TypeScript definitions available in `src/types/api.ts` for proper typing
- Electron security model enforced - no nodeIntegration in renderer process
- Maintain existing user interface and workflow patterns
- Ensure data consistency during transition from localStorage to database
- Performance must remain acceptable for large datasets (1000+ leads)

### Testing
**Test file location:** `src/utils/__tests__/` and `src/components/__tests__/`
**Test standards:** Follow patterns established in Stories 3.1, 3.2, and 3.3
**Testing frameworks and patterns to use:** Vitest and React Testing Library (based on PRD requirements)
**Specific testing requirements:**
- Unit tests for refactored utility functions with mocked window.api
- Component tests for async data loading and error handling
- Integration tests for complete user workflows with database backend
- Error handling tests for various failure scenarios
- Performance tests for large dataset operations
- Data migration tests from localStorage to database operations

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-09-16 | v1.0 | Initial story creation | Scrum Master |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

## QA Results

*This section will be populated by the QA agent after story completion*