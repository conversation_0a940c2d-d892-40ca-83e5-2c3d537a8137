import { context<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON> } from 'electron';
import type { IpcResponse } from '../main/ipc/utils';
import type {
  ImportRecord,
  ImportFilters,
  LeadRecord,
  LeadFilters,
  BulkLeadData,
  GeneratedContentRecord,
  ContentFilters,
  MappingRecord,
  MappingFilters,
  AppMetadataRecord,
  MetadataFilters,
  SearchFilters
} from '../database/dal';

// Define the API interface that will be exposed to the renderer
export interface ElectronAPI {
  // Imports operations
  imports: {
    create: (data: Omit<ImportRecord, 'id' | 'created_at' | 'updated_at'>) => Promise<IpcResponse<ImportRecord>>;
    getAll: (options?: ImportFilters) => Promise<IpcResponse<ImportRecord[]>>;
    getById: (id: number) => Promise<IpcResponse<ImportRecord | null>>;
    update: (id: number, data: Partial<ImportRecord>) => Promise<IpcResponse<boolean>>;
    delete: (id: number) => Promise<IpcResponse<boolean>>;
  };
  
  // Leads operations
  leads: {
    create: (data: Omit<LeadRecord, 'id' | 'created_at' | 'updated_at'>) => Promise<IpcResponse<LeadRecord>>;
    bulkCreate: (leads: BulkLeadData[]) => Promise<IpcResponse<{ created: number; errors: any[] }>>;
    getAll: (options?: LeadFilters) => Promise<IpcResponse<LeadRecord[]>>;
    getById: (id: number) => Promise<IpcResponse<LeadRecord | null>>;
    getByImport: (importId: number) => Promise<IpcResponse<LeadRecord[]>>;
    update: (id: number, data: Partial<LeadRecord>) => Promise<IpcResponse<boolean>>;
    delete: (id: number) => Promise<IpcResponse<boolean>>;
    search: (query: string, options?: LeadFilters) => Promise<IpcResponse<LeadRecord[]>>;
  };
  
  // Generated content operations
  content: {
    create: (data: Omit<GeneratedContentRecord, 'id' | 'created_at' | 'updated_at'>) => Promise<IpcResponse<GeneratedContentRecord>>;
    getByLead: (leadId: number) => Promise<IpcResponse<GeneratedContentRecord[]>>;
    getByTouchpoint: (touchpoint: number, options?: ContentFilters) => Promise<IpcResponse<GeneratedContentRecord[]>>;
    update: (id: number, data: Partial<GeneratedContentRecord>) => Promise<IpcResponse<boolean>>;
    delete: (id: number) => Promise<IpcResponse<boolean>>;
  };
  
  // Mappings operations
  mappings: {
    create: (data: Omit<MappingRecord, 'id' | 'created_at' | 'updated_at'>) => Promise<IpcResponse<MappingRecord>>;
    getByImport: (importId: number) => Promise<IpcResponse<MappingRecord[]>>;
    getActive: (options?: MappingFilters) => Promise<IpcResponse<MappingRecord[]>>;
    update: (id: number, data: Partial<MappingRecord>) => Promise<IpcResponse<boolean>>;
    delete: (id: number) => Promise<IpcResponse<boolean>>;
  };
  
  // App metadata operations
  metadata: {
    get: (key: string) => Promise<IpcResponse<any>>;
    set: (key: string, value: any) => Promise<IpcResponse<boolean>>;
    delete: (key: string) => Promise<IpcResponse<boolean>>;
    getAll: (options?: MetadataFilters) => Promise<IpcResponse<AppMetadataRecord[]>>;
  };
  
  // Advanced queries
  queries: {
    getLeadsWithContent: (options?: SearchFilters) => Promise<IpcResponse<any[]>>;
    getImportSummary: (importId?: number) => Promise<IpcResponse<any>>;
    getContentStats: () => Promise<IpcResponse<any>>;
    exportData: (format: string, options?: any) => Promise<IpcResponse<any>>;
  };
}

// Create the API object with all database operations
const electronAPI: ElectronAPI = {
  imports: {
    create: (data) => ipcRenderer.invoke('ipc:imports:create', data),
    getAll: (options) => ipcRenderer.invoke('ipc:imports:getAll', options),
    getById: (id) => ipcRenderer.invoke('ipc:imports:getById', id),
    update: (id, data) => ipcRenderer.invoke('ipc:imports:update', id, data),
    delete: (id) => ipcRenderer.invoke('ipc:imports:delete', id),
  },
  
  leads: {
    create: (data) => ipcRenderer.invoke('ipc:leads:create', data),
    bulkCreate: (leads) => ipcRenderer.invoke('ipc:leads:bulkCreate', leads),
    getAll: (options) => ipcRenderer.invoke('ipc:leads:getAll', options),
    getById: (id) => ipcRenderer.invoke('ipc:leads:getById', id),
    getByImport: (importId) => ipcRenderer.invoke('ipc:leads:getByImport', importId),
    update: (id, data) => ipcRenderer.invoke('ipc:leads:update', id, data),
    delete: (id) => ipcRenderer.invoke('ipc:leads:delete', id),
    search: (query, options) => ipcRenderer.invoke('ipc:leads:search', query, options),
  },
  
  content: {
    create: (data) => ipcRenderer.invoke('ipc:content:create', data),
    getByLead: (leadId) => ipcRenderer.invoke('ipc:content:getByLead', leadId),
    getByTouchpoint: (touchpoint, options) => ipcRenderer.invoke('ipc:content:getByTouchpoint', touchpoint, options),
    update: (id, data) => ipcRenderer.invoke('ipc:content:update', id, data),
    delete: (id) => ipcRenderer.invoke('ipc:content:delete', id),
  },
  
  mappings: {
    create: (data) => ipcRenderer.invoke('ipc:mappings:create', data),
    getByImport: (importId) => ipcRenderer.invoke('ipc:mappings:getByImport', importId),
    getActive: (options) => ipcRenderer.invoke('ipc:mappings:getActive', options),
    update: (id, data) => ipcRenderer.invoke('ipc:mappings:update', id, data),
    delete: (id) => ipcRenderer.invoke('ipc:mappings:delete', id),
  },
  
  metadata: {
    get: (key) => ipcRenderer.invoke('ipc:metadata:get', key),
    set: (key, value) => ipcRenderer.invoke('ipc:metadata:set', key, value),
    delete: (key) => ipcRenderer.invoke('ipc:metadata:delete', key),
    getAll: (options) => ipcRenderer.invoke('ipc:metadata:getAll', options),
  },
  
  queries: {
    getLeadsWithContent: (options) => ipcRenderer.invoke('ipc:queries:getLeadsWithContent', options),
    getImportSummary: (importId) => ipcRenderer.invoke('ipc:queries:getImportSummary', importId),
    getContentStats: () => ipcRenderer.invoke('ipc:queries:getContentStats'),
    exportData: (format, options) => ipcRenderer.invoke('ipc:queries:exportData', format, options),
  },
};

// Expose the API to the renderer process through contextBridge
contextBridge.exposeInMainWorld('api', electronAPI);

// Also expose some utility functions
contextBridge.exposeInMainWorld('electronUtils', {
  platform: process.platform,
  versions: process.versions,
});

// Type declaration for global window object (for TypeScript)
declare global {
  interface Window {
    api: ElectronAPI;
    electronUtils: {
      platform: string;
      versions: NodeJS.ProcessVersions;
    };
  }
}
