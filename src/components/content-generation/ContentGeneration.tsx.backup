import { useState, useEffect, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Loader2, Sparkles, Edit, Save, X, Eye, Copy } from 'lucide-react'
import { contentGenerationService } from '@/services/contentGenerationService'
import type { LeadData } from '@/types/lead'
import type { ClaudeResponse } from '@/services/claudeService'

interface ContentGenerationProps {
  lead: LeadData
  onStatusUpdate?: (leadId: string, status: LeadData['status']) => void
}

interface SnippetConfig {
  key: keyof ClaudeResponse
  label: string
  description: string
  isHtml: boolean
  timeline: string
}

const SNIPPETS: SnippetConfig[] = [
  {
    key: 'snippet1',
    label: 'Email Subject',
    description: 'Day 1 - Initial outreach subject line',
    isHtml: false,
    timeline: 'Day 1',
  },
  {
    key: 'snippet2',
    label: 'Email Body',
    description: 'Day 1 - Initial outreach email body',
    isHtml: true,
    timeline: 'Day 1',
  },
  {
    key: 'snippet3',
    label: 'LinkedIn Message',
    description: 'Day 3 - LinkedIn connection message',
    isHtml: false,
    timeline: 'Day 3',
  },
  {
    key: 'snippet4',
    label: 'Bump Email',
    description: 'Day 7 - First follow-up email',
    isHtml: true,
    timeline: 'Day 7',
  },
  {
    key: 'snippet5',
    label: 'Follow-up Email',
    description: 'Day 12 - Second follow-up email',
    isHtml: true,
    timeline: 'Day 12',
  },
  {
    key: 'snippet6',
    label: 'Second Bump',
    description: 'Day 17 - Third follow-up email',
    isHtml: true,
    timeline: 'Day 17',
  },
  {
    key: 'snippet7',
    label: 'Breakup Email',
    description: 'Day 25 - Final follow-up email',
    isHtml: true,
    timeline: 'Day 25',
  },
]

export function ContentGeneration({
  lead,
  onStatusUpdate,
}: ContentGenerationProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [content, setContent] = useState<ClaudeResponse | null>(null)
  const [editingSnippet, setEditingSnippet] = useState<string | null>(null)
  const [editedContent, setEditedContent] = useState<Partial<ClaudeResponse>>(
    {}
  )
  const [viewMode, setViewMode] = useState<'preview' | 'edit'>('preview')
  const [error, setError] = useState<string | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [customPrompt, setCustomPrompt] = useState('')
  const [systemPrompt, setSystemPrompt] = useState('')
  const [isEditingSystemPrompt, setIsEditingSystemPrompt] = useState(false)
  const [editedSystemPrompt, setEditedSystemPrompt] = useState('')

  const getFieldValue = useCallback(
    (field: string): string => {
      // Try to get the value from lead data, handling potential column mapping
      const directValue = (lead as Record<string, unknown>)[field]
      if (directValue) return String(directValue)

      // Try common variations
      const variations = {
        company: ['company_name', 'organization', 'company'],
        contact: ['name', 'full_name', 'contact_name', 'first_name'],
        email: ['email_address', 'email'],
        title: ['job_title', 'position', 'title'],
        industry: ['industry', 'sector', 'vertical'],
        linkedin: ['linkedin_url', 'linkedin_profile', 'linkedin'],
      }

      const fieldVariations = variations[field as keyof typeof variations] || [
        field,
      ]

      for (const variant of fieldVariations) {
        const value = (lead as Record<string, unknown>)[variant]
        if (value) return String(value)
      }

      return ''
    },
    [lead]
  )

  // Load existing content on mount
  useEffect(() => {
    const leadId = btoa(String(lead.email || lead.id)).replace(/[/+=]/g, '')
    const existingContent = contentGenerationService.getLeadContent(leadId)
    if (existingContent) {
      setContent(existingContent)
    }

    // Set default system prompt
    const defaultSystemPrompt = `You are a sales email expert. Generate a professional 6-touchpoint email sequence for the following prospect:

**PROSPECT DETAILS:**
- Name: ${getFieldValue('contact') || 'N/A'}
- Company: ${getFieldValue('company') || 'N/A'}
- Title: ${getFieldValue('title') || 'N/A'}
- Email: ${getFieldValue('email') || 'N/A'}
- Industry: ${getFieldValue('industry') || 'Technology'}
- LinkedIn: ${getFieldValue('linkedin') || 'N/A'}

**REQUIREMENTS:**
Generate exactly 7 content snippets following this structure:

1. **snippet1**: Day 1 Email SUBJECT LINE (plain text, 36-50 characters)
2. **snippet2**: Day 1 Email BODY (HTML formatted with <div> tags, 150-200 words)
3. **snippet3**: Day 3 LinkedIn message (plain text, under 300 characters)
4. **snippet4**: Day 7 Bump email (HTML formatted, short)
5. **snippet5**: Day 12 Email (HTML formatted, 150-200 words)
6. **snippet6**: Day 17 Bump email (HTML formatted, short)
7. **snippet7**: Day 25 Breakup email (HTML formatted, 150-200 words)

**CONTENT STRATEGY:**
Each email should follow this structure:
- Opening: Personalized reference to their company/industry/role
- Peer Proof: Reference similar companies who have succeeded
- Their ROI: Specific value proposition for their situation
- Soft CTA: Low-pressure next step

**OUTPUT FORMAT:**
Respond with valid JSON containing ALL prospect fields plus the 7 snippets.

**IMPORTANT:**
- Use only the prospect information provided
- Keep content professional and value-focused
- Ensure all HTML is properly formatted with <div> tags
- Subject line must be 36-50 characters
- LinkedIn message must be under 300 characters
- Return valid JSON only, no other text`

    setSystemPrompt(defaultSystemPrompt)
    setEditedSystemPrompt(defaultSystemPrompt)

    // Set default custom prompt
    const leadName = getFieldValue('contact') || 'this lead'
    setCustomPrompt(`Tell me about ${leadName}`)
  }, [lead.email, lead.id, getFieldValue])

  const generateContent = async () => {
    if (!lead.email || !lead.company) {
      setError(
        'Lead must have email and company information to generate content'
      )
      return
    }

    setIsGenerating(true)
    setError(null)
    setIsModalOpen(false)

    try {
      const leadData = {
        first_name: getFieldValue('contact')?.split(' ')[0] || 'There',
        last_name:
          getFieldValue('contact')?.split(' ').slice(1).join(' ') || '',
        company: getFieldValue('company') || '',
        title: getFieldValue('title') || '',
        email: getFieldValue('email') || '',
        industry: getFieldValue('industry') || 'Technology',
        linkedin_url: getFieldValue('linkedin') || '',
      }

      const result = await contentGenerationService.generateForLead(leadData)

      if (result.status === 'completed' && result.content) {
        setContent(result.content)
        onStatusUpdate?.(lead.id, 'drafted')
      } else {
        setError(result.error || 'Failed to generate content')
      }
    } catch (error) {
      setError(
        error instanceof Error ? error.message : 'An unexpected error occurred'
      )
    } finally {
      setIsGenerating(false)
    }
  }

  const handleOpenModal = () => {
    const leadName = getFieldValue('contact') || 'this lead'
    setCustomPrompt(`Tell me about ${leadName}`)
    setIsModalOpen(true)
  }

  const handleSaveSystemPrompt = () => {
    setSystemPrompt(editedSystemPrompt)
    setIsEditingSystemPrompt(false)
  }

  const handleCancelSystemPromptEdit = () => {
    setEditedSystemPrompt(systemPrompt)
    setIsEditingSystemPrompt(false)
  }

  const startEditing = (snippetKey: string) => {
    setEditingSnippet(snippetKey)
    setEditedContent({
      ...editedContent,
      [snippetKey]: content?.[snippetKey as keyof ClaudeResponse] || '',
    })
  }

  const saveEdit = (snippetKey: string) => {
    if (!content) return

    const updatedContent = {
      ...content,
      [snippetKey]: editedContent[snippetKey as keyof ClaudeResponse],
    }

    setContent(updatedContent)

    // Update localStorage
    const leadId = btoa(String(lead.email || lead.id)).replace(/[/+=]/g, '')
    const data = {
      ...updatedContent,
      generatedAt: new Date().toISOString(),
    }
    localStorage.setItem(`lead_content_${leadId}`, JSON.stringify(data))

    setEditingSnippet(null)
  }

  const cancelEdit = () => {
    setEditingSnippet(null)
    setEditedContent({})
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
    } catch {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = text
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }
  }

  const renderContent = (snippet: SnippetConfig) => {
    const snippetContent = content?.[snippet.key]
    const isEditing = editingSnippet === snippet.key
    const displayContent = isEditing
      ? editedContent[snippet.key] || ''
      : snippetContent || ''

    if (!snippetContent && !isEditing) return null

    return (
      <Card key={snippet.key} className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {snippet.timeline}
                </Badge>
                {snippet.label}
              </CardTitle>
              <CardDescription className="text-xs">
                {snippet.description}
              </CardDescription>
            </div>
            <div className="flex items-center gap-1">
              {!isEditing && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(String(snippetContent))}
                    className="h-8 w-8 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => startEditing(String(snippet.key))}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                </>
              )}
              {isEditing && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => saveEdit(String(snippet.key))}
                    className="h-8 w-8 p-0"
                  >
                    <Save className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={cancelEdit}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {isEditing ? (
            <Textarea
              value={String(displayContent)}
              onChange={(e) =>
                setEditedContent({
                  ...editedContent,
                  [snippet.key]: e.target.value,
                })
              }
              rows={snippet.isHtml ? 6 : 3}
              className="min-h-[80px]"
            />
          ) : (
            <div className="space-y-2">
              {snippet.isHtml ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-muted-foreground mb-1">
                      Preview:
                    </div>
                    <div
                      className="text-sm border rounded p-3 bg-muted/20 min-h-[80px]"
                      dangerouslySetInnerHTML={{
                        __html: String(displayContent),
                      }}
                    />
                  </div>
                  <div>
                    <div className="text-xs text-muted-foreground mb-1">
                      HTML Source:
                    </div>
                    <div className="text-xs font-mono border rounded p-3 bg-muted/50 min-h-[80px] overflow-auto">
                      {String(displayContent)}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-sm border rounded p-3 bg-muted/20 min-h-[60px]">
                  {String(displayContent)}
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // Don't show anything if lead doesn't have required fields
  const hasRequiredFields =
    lead.email && (getFieldValue('company') || getFieldValue('contact'))

  if (!hasRequiredFields) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Email Sequence Content
            </CardTitle>
            <CardDescription>
              AI-generated 6-touchpoint email sequence with 7 content snippets
            </CardDescription>
          </div>
          {content && (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setViewMode(viewMode === 'preview' ? 'edit' : 'preview')
                }
              >
                {viewMode === 'preview' ? (
                  <Edit className="h-4 w-4 mr-2" />
                ) : (
                  <Eye className="h-4 w-4 mr-2" />
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="mb-4 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
            {error}
          </div>
        )}

        {!content && !isGenerating && (
          <div className="text-center py-6">
            <Sparkles className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Generate Email Sequence
            </h3>
            <p className="text-muted-foreground mb-4">
              Create a personalized 6-touchpoint email sequence for{' '}
              {getFieldValue('contact') || 'this lead'}
              {getFieldValue('company') && ` at ${getFieldValue('company')}`}
            </p>
            <Dialog
              open={isModalOpen}
              onOpenChange={(open) => {
                if (open) {
                  const leadName = getFieldValue('contact') || 'this lead'
                  setCustomPrompt(`Tell me about ${leadName}`)
                }
                setIsModalOpen(open)
              }}
            >
              <DialogTrigger asChild>
                <Button className="gap-2">
                  <Sparkles className="h-4 w-4" />
                  Generate Content
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Generate Email Sequence</DialogTitle>
                  <DialogDescription>
                    Customize your prompt and system settings for personalized
                    content generation
                  </DialogDescription>
                </DialogHeader>

                <div className="space-y-6">
                  {/* Custom Prompt Section */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Your Prompt</label>
                    <Textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="Enter your custom prompt here..."
                      rows={4}
                      className="min-h-[100px]"
                    />
                  </div>

                  {/* System Prompt Accordion */}
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="system-prompt">
                      <AccordionTrigger className="text-sm font-medium">
                        System Prompt Settings
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-muted-foreground">
                              Configure the system prompt that guides content
                              generation
                            </p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setIsEditingSystemPrompt(!isEditingSystemPrompt)
                              }
                            >
                              {isEditingSystemPrompt ? 'Cancel' : 'Edit'}
                            </Button>
                          </div>

                          {isEditingSystemPrompt ? (
                            <div className="space-y-3">
                              <Textarea
                                value={editedSystemPrompt}
                                onChange={(e) =>
                                  setEditedSystemPrompt(e.target.value)
                                }
                                rows={12}
                                className="min-h-[300px] font-mono text-xs"
                              />
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  onClick={handleSaveSystemPrompt}
                                >
                                  <Save className="h-4 w-4 mr-2" />
                                  Save Changes
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={handleCancelSystemPromptEdit}
                                >
                                  <X className="h-4 w-4 mr-2" />
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="bg-muted/50 rounded-lg p-4">
                              <pre className="text-xs whitespace-pre-wrap font-mono text-muted-foreground max-h-[200px] overflow-y-auto">
                                {systemPrompt}
                              </pre>
                            </div>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </div>

                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsModalOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => generateContent()}
                    disabled={isGenerating}
                    className="gap-2"
                  >
                    {isGenerating ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Sparkles className="h-4 w-4" />
                    )}
                    Generate
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        )}

        {isGenerating && (
          <div className="text-center py-6">
            <Loader2 className="h-8 w-8 mx-auto animate-spin text-primary mb-4" />
            <h3 className="text-lg font-medium mb-2">Generating Content...</h3>
            <p className="text-muted-foreground">
              Creating personalized email sequence for{' '}
              {getFieldValue('contact') || 'this lead'}
            </p>
          </div>
        )}

        {content && (
          <div className="space-y-4">
            <div className="flex items-center justify-between pb-2 border-b">
              <div>
                <p className="text-sm font-medium">Generated Content</p>
                <p className="text-xs text-muted-foreground">
                  7 snippets for 6-touchpoint email sequence
                </p>
              </div>
              <Dialog
                open={isModalOpen}
                onOpenChange={(open) => {
                  if (open) {
                    const leadName = getFieldValue('contact') || 'this lead'
                    setCustomPrompt(`Tell me about ${leadName}`)
                  }
                  setIsModalOpen(open)
                }}
              >
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={isGenerating}
                    className="gap-2"
                  >
                    <Sparkles className="h-4 w-4" />
                    Regenerate
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Generate Email Sequence</DialogTitle>
                    <DialogDescription>
                      Customize your prompt and system settings for personalized
                      content generation
                    </DialogDescription>
                  </DialogHeader>

                  <div className="space-y-6">
                    {/* Custom Prompt Section */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Your Prompt</label>
                      <Textarea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="Enter your custom prompt here..."
                        rows={4}
                        className="min-h-[100px]"
                      />
                    </div>

                    {/* System Prompt Accordion */}
                    <Accordion type="single" collapsible className="w-full">
                      <AccordionItem value="system-prompt">
                        <AccordionTrigger className="text-sm font-medium">
                          System Prompt Settings
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-muted-foreground">
                                Configure the system prompt that guides content
                                generation
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  setIsEditingSystemPrompt(
                                    !isEditingSystemPrompt
                                  )
                                }
                              >
                                {isEditingSystemPrompt ? 'Cancel' : 'Edit'}
                              </Button>
                            </div>

                            {isEditingSystemPrompt ? (
                              <div className="space-y-3">
                                <Textarea
                                  value={editedSystemPrompt}
                                  onChange={(e) =>
                                    setEditedSystemPrompt(e.target.value)
                                  }
                                  rows={12}
                                  className="min-h-[300px] font-mono text-xs"
                                />
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    onClick={handleSaveSystemPrompt}
                                  >
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Changes
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleCancelSystemPromptEdit}
                                  >
                                    <X className="h-4 w-4 mr-2" />
                                    Cancel
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="bg-muted/50 rounded-lg p-4">
                                <pre className="text-xs whitespace-pre-wrap font-mono text-muted-foreground max-h-[200px] overflow-y-auto">
                                  {systemPrompt}
                                </pre>
                              </div>
                            )}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                  </div>

                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsModalOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={() => generateContent()}
                      disabled={isGenerating}
                      className="gap-2"
                    >
                      {isGenerating ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Sparkles className="h-4 w-4" />
                      )}
                      Generate
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            <div className="space-y-4">{SNIPPETS.map(renderContent)}</div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
