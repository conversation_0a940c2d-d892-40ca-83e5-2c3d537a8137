name: Build and Deploy

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run tests
      run: npm test

    - name: Build application
      run: npm run build
      env:
        VITE_API_BASE_URL: ${{ vars.VITE_API_BASE_URL || 'http://localhost:3000/api' }}
        VITE_AUTH_ENABLED: ${{ vars.VITE_AUTH_ENABLED || 'false' }}
        VITE_ENABLE_DEBUG: ${{ vars.VITE_ENABLE_DEBUG || 'false' }}
        VITE_CLAUDE_API_KEY: ${{ secrets.VITE_CLAUDE_API_KEY }}
        VITE_WOODPECKER_API_KEY: ${{ secrets.VITE_WOODPECKER_API_KEY }}

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-files
        path: dist/

    - name: Deploy to production
      run: |
        echo "Deploy step would go here"
        echo "Example: Deploy to S3, Vercel, Netlify, etc."
        # Add your actual deployment commands here